import { FileText, Setting<PERSON>, User } from "lucide-react";

export default function AppHeader() {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4 sticky top-0 z-50">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded flex items-center justify-center">
              <FileText className="text-white text-sm" />
            </div>
            <h1 className="text-xl font-semibold text-gray-900">NotebookLM Clone</h1>
          </div>
          <span className="text-sm text-gray-500 hidden md:inline">PDF Chat Assistant</span>
        </div>
        
        <div className="flex items-center space-x-4">
          <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <Settings className="text-gray-600 w-5 h-5" />
          </button>
          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
            <User className="text-white w-4 h-4" />
          </div>
        </div>
      </div>
    </header>
  );
}
