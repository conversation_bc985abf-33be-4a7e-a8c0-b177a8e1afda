import { type Document, type InsertDocument, type ChatMessage, type InsertChatMessage } from "@shared/schema";
import { randomUUID } from "crypto";

export interface IStorage {
  // Document operations
  createDocument(document: InsertDocument): Promise<Document>;
  getDocument(id: string): Promise<Document | undefined>;
  getDocuments(): Promise<Document[]>;
  deleteDocument(id: string): Promise<void>;
  
  // Chat message operations
  createChatMessage(message: InsertChatMessage): Promise<ChatMessage>;
  getChatMessages(documentId: string): Promise<ChatMessage[]>;
  deleteChatMessages(documentId: string): Promise<void>;
}

export class MemStorage implements IStorage {
  private documents: Map<string, Document>;
  private chatMessages: Map<string, ChatMessage>;

  constructor() {
    this.documents = new Map();
    this.chatMessages = new Map();
  }

  async createDocument(insertDocument: InsertDocument): Promise<Document> {
    const id = randomUUID();
    const document: Document = {
      ...insertDocument,
      id,
      uploadedAt: new Date(),
    };
    this.documents.set(id, document);
    return document;
  }

  async getDocument(id: string): Promise<Document | undefined> {
    return this.documents.get(id);
  }

  async getDocuments(): Promise<Document[]> {
    return Array.from(this.documents.values()).sort(
      (a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime()
    );
  }

  async deleteDocument(id: string): Promise<void> {
    this.documents.delete(id);
    // Also delete associated chat messages
    await this.deleteChatMessages(id);
  }

  async createChatMessage(insertMessage: InsertChatMessage): Promise<ChatMessage> {
    const id = randomUUID();
    const message: ChatMessage = {
      ...insertMessage,
      id,
      createdAt: new Date(),
      citations: insertMessage.citations || null,
    };
    this.chatMessages.set(id, message);
    return message;
  }

  async getChatMessages(documentId: string): Promise<ChatMessage[]> {
    return Array.from(this.chatMessages.values())
      .filter(message => message.documentId === documentId)
      .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
  }

  async deleteChatMessages(documentId: string): Promise<void> {
    const messagesToDelete = Array.from(this.chatMessages.values())
      .filter(message => message.documentId === documentId);
    
    messagesToDelete.forEach(message => {
      this.chatMessages.delete(message.id);
    });
  }
}

export const storage = new MemStorage();
