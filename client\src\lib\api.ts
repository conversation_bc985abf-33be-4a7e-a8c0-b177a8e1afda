import { apiRequest } from "./queryClient";

export async function uploadDocument(file: File): Promise<Document> {
  const formData = new FormData();
  formData.append('pdf', file);
  
  const response = await fetch('/api/documents/upload', {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Upload failed');
  }

  return response.json();
}

export async function sendChatMessage(documentId: string, message: string) {
  const response = await apiRequest('POST', `/api/documents/${documentId}/chat`, {
    message,
  });
  return response.json();
}

export async function deleteDocument(documentId: string) {
  await apiRequest('DELETE', `/api/documents/${documentId}`);
}
