import { useState, useRef, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Send, Bot, User } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { sendChatMessage } from "@/lib/api";
import { ChatMessage, Document } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ChatInterfaceProps {
  document: Document | null;
  onCitationClick: (page: number) => void;
}

export default function ChatInterface({ document, onCitationClick }: ChatInterfaceProps) {
  const [message, setMessage] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: messages = [], isLoading } = useQuery<ChatMessage[]>({
    queryKey: ["/api/documents", document?.id, "messages"],
    enabled: !!document,
  });

  const chatMutation = useMutation({
    mutationFn: (msg: string) => sendChatMessage(document!.id, msg),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["/api/documents", document?.id, "messages"],
      });
      setMessage("");
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSendMessage = () => {
    if (!message.trim() || !document || chatMutation.isPending) return;
    chatMutation.mutate(message);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  if (!document) {
    return (
      <div className="w-96 bg-white flex flex-col">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Chat with Document</h3>
          <p className="text-sm text-gray-500">Upload a PDF to start chatting</p>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center p-6">
            <Bot className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">No document selected</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-96 bg-white flex flex-col">
      {/* Chat Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Chat with Document</h3>
        <p className="text-sm text-gray-500">Ask questions about the PDF content</p>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex space-x-3">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
              <Bot className="text-white w-4 h-4" />
            </div>
            <div className="flex-1">
              <div className="bg-gray-100 rounded-lg p-3">
                <p className="text-sm text-gray-900">
                  Hello! I've analyzed your document "{document.originalName}". 
                  I can help you understand the content, summarize sections, or answer 
                  specific questions. What would you like to know?
                </p>
              </div>
              <div className="flex items-center mt-2">
                <span className="text-xs text-gray-500">Just now</span>
              </div>
            </div>
          </div>
        ) : (
          messages.map((msg) => (
            <div
              key={msg.id}
              className={`flex space-x-3 ${
                msg.role === "user" ? "justify-end" : ""
              }`}
            >
              {msg.role === "assistant" && (
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                  <Bot className="text-white w-4 h-4" />
                </div>
              )}
              
              <div className={`flex-1 ${msg.role === "user" ? "max-w-xs" : ""}`}>
                <div
                  className={`rounded-lg p-3 ${
                    msg.role === "user"
                      ? "bg-primary text-white ml-auto"
                      : "bg-gray-100 text-gray-900"
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                </div>
                
                <div className={`flex items-center mt-2 space-x-2 ${
                  msg.role === "user" ? "justify-end" : ""
                }`}>
                  <span className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(msg.createdAt), { addSuffix: true })}
                  </span>
                  
                  {/* Citations */}
                  {msg.citations && msg.citations.length > 0 && (
                    <div className="flex space-x-1">
                      {msg.citations.map((page) => (
                        <button
                          key={page}
                          onClick={() => onCitationClick(page)}
                          className="bg-blue-100 text-primary text-xs px-2 py-1 rounded font-mono hover:bg-blue-200 transition-colors"
                        >
                          Page {page}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              
              {msg.role === "user" && (
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                  <User className="text-white w-4 h-4" />
                </div>
              )}
            </div>
          ))
        )}

        {/* Loading State */}
        {chatMutation.isPending && (
          <div className="flex space-x-3">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
              <Bot className="text-white w-4 h-4" />
            </div>
            <div className="flex-1">
              <div className="bg-gray-100 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse animation-delay-200"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse animation-delay-400"></div>
                  <span className="text-sm text-gray-500">AI is analyzing the document...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Chat Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <Input
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask a question about the document..."
            className="flex-1"
            disabled={chatMutation.isPending}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!message.trim() || chatMutation.isPending}
            size="sm"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        
        {/* Token Usage Info */}
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <span>Press Enter to send</span>
          <span>Messages: {messages.length}</span>
        </div>
      </div>
    </div>
  );
}
