import { useState } from "react";
import { ZoomIn, ZoomOut, ChevronLeft, ChevronRight, Expand } from "lucide-react";
import { Document } from "@/types";

interface PDFViewerProps {
  document: Document | null;
  highlightedPage?: number;
}

export default function PDFViewer({ document, highlightedPage }: PDFViewerProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [zoomLevel, setZoomLevel] = useState(100);

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 25, 50));
  };

  const handlePreviousPage = () => {
    if (document) {
      setCurrentPage(prev => Math.max(prev - 1, 1));
    }
  };

  const handleNextPage = () => {
    if (document) {
      setCurrentPage(prev => Math.min(prev + 1, document.pageCount));
    }
  };

  // Navigate to highlighted page when citation is clicked
  if (highlightedPage && highlightedPage !== currentPage) {
    setCurrentPage(highlightedPage);
  }

  if (!document) {
    return (
      <div className="flex-1 bg-white border-r border-gray-200 flex flex-col">
        <div className="flex-1 flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center mx-auto mb-4">
              <div className="w-8 h-10 bg-gray-400 rounded"></div>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Document Selected</h3>
            <p className="text-gray-500">Upload a PDF to start viewing and chatting</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-white border-r border-gray-200 flex flex-col">
      {/* PDF Controls */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-medium text-gray-900">{document.originalName}</h2>
            <span className="text-sm text-gray-500">
              Page {currentPage} of {document.pageCount}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Zoom Controls */}
            <button
              onClick={handleZoomOut}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <ZoomOut className="w-4 h-4 text-gray-600" />
            </button>
            <span className="text-sm text-gray-600 px-2">{zoomLevel}%</span>
            <button
              onClick={handleZoomIn}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <ZoomIn className="w-4 h-4 text-gray-600" />
            </button>
            
            {/* Navigation Controls */}
            <div className="w-px h-6 bg-gray-300 mx-2"></div>
            <button
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4 text-gray-600" />
            </button>
            <button
              onClick={handleNextPage}
              disabled={currentPage === document.pageCount}
              className="p-2 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="w-4 h-4 text-gray-600" />
            </button>
            
            {/* Additional Controls */}
            <div className="w-px h-6 bg-gray-300 mx-2"></div>
            <button className="p-2 hover:bg-gray-200 rounded-lg transition-colors">
              <Expand className="w-4 h-4 text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* PDF Display Area */}
      <div className="flex-1 overflow-auto bg-gray-100 p-6">
        <div className="max-w-4xl mx-auto">
          {/* PDF Page Mock - In a real implementation, this would use PDF.js */}
          <div
            className="bg-white shadow-lg rounded-lg overflow-hidden mb-6 transition-transform"
            style={{ transform: `scale(${zoomLevel / 100})`, transformOrigin: 'top center' }}
          >
            <div className="p-8">
              <div className="text-center mb-8">
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                  Document Content - Page {currentPage}
                </h1>
                <p className="text-sm text-gray-600">{document.originalName}</p>
              </div>
              
              <div className="prose prose-sm max-w-none">
                <p className="text-gray-700 leading-relaxed mb-4">
                  This is a simulated view of page {currentPage} of the PDF document. 
                  In a real implementation, this would show the actual PDF content using 
                  a library like PDF.js or react-pdf.
                </p>
                
                <p className="text-gray-700 leading-relaxed mb-4">
                  The document contains {document.pageCount} pages and has been processed 
                  for text extraction and analysis. You can navigate through the pages 
                  using the controls above.
                </p>
                
                {/* Show some of the actual extracted content */}
                <div className="bg-gray-50 p-4 rounded border-l-4 border-blue-500">
                  <h3 className="font-semibold mb-2">Extracted Content Preview:</h3>
                  <p className="text-sm text-gray-700">
                    {document.content.slice(0, 500)}...
                  </p>
                </div>
                
                {highlightedPage === currentPage && (
                  <div className="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded">
                    <p className="text-sm text-yellow-800">
                      📍 This page was referenced in the chat conversation
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Page indicator */}
          <div className="text-center text-sm text-gray-500">
            Page {currentPage} of {document.pageCount}
          </div>
        </div>
      </div>
    </div>
  );
}
