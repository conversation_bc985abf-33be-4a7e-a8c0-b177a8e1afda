# replit.md

## Overview

This is a Google NotebookLM clone - a web-based application that enables users to upload and interact with PDF documents through a chat interface. The system provides PDF viewing capabilities with citation-based navigation and AI-powered conversational features for document analysis.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript running on Vite
- **UI Library**: Radix UI components with shadcn/ui styling system
- **Styling**: Tailwind CSS with CSS variables for theming
- **State Management**: TanStack Query (React Query) for server state
- **Routing**: Wouter for lightweight client-side routing
- **Build Tool**: Vite with custom configuration for development and production

### Backend Architecture
- **Runtime**: Node.js with Express.js server
- **Language**: TypeScript with ES modules
- **API Pattern**: RESTful API with structured route handlers
- **File Upload**: Multer middleware for PDF file handling
- **Development**: Custom Vite integration for hot module replacement

### Data Storage Solutions
- **Database**: PostgreSQL with Drizzle ORM
- **Connection**: Neon Database serverless driver
- **Schema Management**: Drizzle Kit for migrations and schema management
- **Development Storage**: In-memory storage implementation for rapid prototyping
- **File Storage**: Local filesystem for uploaded PDF files

## Key Components

### Document Management
- **PDF Upload**: Multi-part form upload with file validation (10MB limit, PDF only)
- **PDF Processing**: Text extraction and chunking for RAG (Retrieval-Augmented Generation)
- **Document Storage**: Metadata and content storage with file system integration

### Chat System
- **Conversational AI**: OpenAI GPT-4o integration for document analysis
- **Context Awareness**: RAG implementation using document chunks
- **Citation System**: Page-level citations with interactive navigation
- **Message History**: Persistent conversation storage per document

### PDF Viewer
- **Document Display**: Built-in PDF viewer with navigation controls
- **Zoom Controls**: Adjustable zoom levels (50%-200%)
- **Page Navigation**: Previous/next page controls with page indicators
- **Citation Navigation**: Click-to-navigate from chat citations

### User Interface
- **Layout**: Three-panel layout (sidebar, viewer, chat)
- **Components**: Modular component architecture using shadcn/ui
- **Responsiveness**: Mobile-friendly design with responsive breakpoints
- **Accessibility**: ARIA labels and keyboard navigation support

## Data Flow

### Document Upload Flow
1. User selects PDF file through upload interface
2. Frontend validates file type and size constraints
3. File uploaded via multipart/form-data to `/api/documents/upload`
4. Backend processes PDF: extracts text, creates chunks, counts pages
5. Document metadata and content stored in database
6. Frontend updates document list and selects new document

### Chat Interaction Flow
1. User types question in chat interface
2. Frontend sends message to `/api/documents/{id}/chat`
3. Backend retrieves document chunks for context
4. OpenAI API generates response with citations
5. Both user and AI messages stored in database
6. Frontend displays conversation with clickable citations

### Citation Navigation Flow
1. User clicks citation button in AI response
2. Frontend extracts page number from citation
3. PDF viewer navigates to referenced page
4. Page highlighting indicates active citation

## External Dependencies

### AI Services
- **OpenAI API**: GPT-4o model for document analysis and chat responses
- **API Key Management**: Environment variable configuration
- **Token Optimization**: Chunk-based context to minimize token usage

### Database Services
- **Neon Database**: Serverless PostgreSQL hosting
- **Connection Management**: Pooled connections via Drizzle ORM
- **Schema Management**: Version-controlled migrations

### UI Dependencies
- **Radix UI**: Primitive components for accessibility
- **Lucide React**: Icon library for consistent iconography
- **TailwindCSS**: Utility-first styling framework
- **React Hook Form**: Form validation and management

### Development Tools
- **Vite**: Build tool with React plugin
- **ESBuild**: Production bundling for server code
- **TypeScript**: Type safety across frontend and backend
- **Drizzle Kit**: Database schema management

## Deployment Strategy

### Build Process
- **Frontend**: Vite builds React app to `dist/public`
- **Backend**: ESBuild bundles server code to `dist/index.js`
- **Assets**: Static files served from build directory

### Environment Configuration
- **Database URL**: PostgreSQL connection string via `DATABASE_URL`
- **OpenAI API Key**: API access via `OPENAI_API_KEY`
- **Node Environment**: Development/production mode switching

### Production Deployment
- **Server**: Express.js serves both API and static files
- **Database**: Automatic schema sync via Drizzle push
- **File Storage**: Local filesystem (expandable to cloud storage)
- **Process Management**: PM2 or similar for production monitoring

### Development Setup
- **Hot Reload**: Vite middleware integration for instant updates
- **API Proxy**: Development server proxies API requests
- **Database**: Local PostgreSQL or remote Neon database
- **File Uploads**: Local `uploads/` directory for development