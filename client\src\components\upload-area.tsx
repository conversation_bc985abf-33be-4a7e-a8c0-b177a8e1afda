import { useState, useRef } from "react";
import { Upload, FileText } from "lucide-react";
import { uploadDocument } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface UploadAreaProps {
  onUploadSuccess?: () => void;
}

export default function UploadArea({ onUploadSuccess }: UploadAreaProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const uploadMutation = useMutation({
    mutationFn: uploadDocument,
    onSuccess: () => {
      toast({
        title: "Success",
        description: "PDF uploaded and processed successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/documents"] });
      onUploadSuccess?.();
    },
    onError: (error: Error) => {
      toast({
        title: "Upload Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    if (file.type !== 'application/pdf') {
      toast({
        title: "Invalid File",
        description: "Please select a PDF file.",
        variant: "destructive",
      });
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: "Please select a PDF file smaller than 10MB.",
        variant: "destructive",
      });
      return;
    }

    uploadMutation.mutate(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="p-6 border-b border-gray-200">
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={handleClick}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragOver
            ? "border-primary bg-blue-50"
            : uploadMutation.isPending
            ? "border-gray-300 bg-gray-50"
            : "border-gray-300 bg-gray-50 hover:border-primary hover:bg-blue-50"
        }`}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
        
        <div className="mb-4">
          {uploadMutation.isPending ? (
            <FileText className="w-10 h-10 text-primary mx-auto animate-pulse" />
          ) : (
            <Upload className="w-10 h-10 text-gray-400 mx-auto" />
          )}
        </div>
        
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {uploadMutation.isPending ? "Processing PDF..." : "Upload PDF Document"}
        </h3>
        
        <p className="text-sm text-gray-500 mb-4">
          {uploadMutation.isPending
            ? "Extracting text and preparing for analysis..."
            : "Drag and drop your PDF here, or click to browse"
          }
        </p>
        
        {!uploadMutation.isPending && (
          <>
            <button className="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
              Choose File
            </button>
            <p className="text-xs text-gray-400 mt-3">Maximum file size: 10MB</p>
          </>
        )}
      </div>
    </div>
  );
}
