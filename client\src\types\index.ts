export interface Document {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  pageCount: number;
  content: string;
  chunks: string[];
  uploadedAt: string;
}

export interface ChatMessage {
  id: string;
  documentId: string;
  role: 'user' | 'assistant';
  content: string;
  citations?: number[] | null;
  createdAt: string;
}

export interface ChatResponse {
  userMessage: ChatMessage;
  aiMessage: ChatMessage;
}
