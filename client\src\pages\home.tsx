import { useState } from "react";
import AppHeader from "@/components/app-header";
import DocumentSidebar from "@/components/document-sidebar";
import PDFViewer from "@/components/pdf-viewer";
import ChatInterface from "@/components/chat-interface";
import { Document } from "@/types";

export default function Home() {
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [highlightedPage, setHighlightedPage] = useState<number | undefined>();

  const handleDocumentSelect = (document: Document) => {
    setSelectedDocument(document);
    setHighlightedPage(undefined);
  };

  const handleCitationClick = (page: number) => {
    setHighlightedPage(page);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <AppHeader />
      
      <div className="flex h-screen pt-16">
        <DocumentSidebar
          selectedDocumentId={selectedDocument?.id}
          onDocumentSelect={handleDocumentSelect}
        />
        
        <div className="flex-1 flex">
          <PDFViewer
            document={selectedDocument}
            highlightedPage={highlightedPage}
          />
          
          <ChatInterface
            document={selectedDocument}
            onCitationClick={handleCitationClick}
          />
        </div>
      </div>
    </div>
  );
}
