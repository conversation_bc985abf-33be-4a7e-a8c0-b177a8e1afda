import { GoogleGenAI } from "@google/genai";

// Using Gemini 2.5 Flash for free AI responses
const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY || "" });

export interface ChatResponse {
  content: string;
  citations: number[];
}

export async function generateChatResponse(
  question: string,
  context: string[],
  conversationHistory: Array<{ role: 'user' | 'assistant'; content: string }>
): Promise<ChatResponse> {
  try {
    const systemPrompt = `You are an AI assistant that helps users understand PDF documents. 
You have access to chunks of text from a PDF document. When answering questions:

1. Base your answers on the provided context chunks
2. Be accurate and concise
3. If you reference specific information, include the chunk number in your response
4. If you cannot answer based on the context, say so clearly
5. Provide specific page citations when possible

Context chunks:
${context.map((chunk, index) => `Chunk ${index + 1}: ${chunk}`).join('\n\n')}

When citing sources, use the format [Chunk X] where X is the chunk number.`;

    // Build conversation history for Gemini
    let conversationText = systemPrompt + "\n\n";
    conversationHistory.forEach(msg => {
      conversationText += `${msg.role === 'user' ? 'Human' : 'Assistant'}: ${msg.content}\n\n`;
    });
    conversationText += `Human: ${question}\n\nAssistant:`;

    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: conversationText,
    });

    const content = response.text || "";
    
    // Extract chunk references to convert to page citations
    const chunkMatches = content.match(/\[Chunk (\d+)\]/g) || [];
    const citations = chunkMatches
      .map((match: string) => {
        const chunkNum = parseInt(match.match(/\d+/)?.[0] || "0");
        // Convert chunk number to approximate page number (rough estimation)
        return Math.ceil(chunkNum / 2); // Assuming ~2 chunks per page
      })
      .filter((page: number, index: number, arr: number[]) => arr.indexOf(page) === index) // Remove duplicates
      .sort((a: number, b: number) => a - b);

    return {
      content: content.replace(/\[Chunk \d+\]/g, ''), // Remove chunk references from final content
      citations,
    };
  } catch (error) {
    throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function summarizeDocument(content: string): Promise<string> {
  try {
    const prompt = `You are an AI assistant that creates concise summaries of documents. Provide a brief overview of the main topics and key points.

Please summarize this document:

${content.slice(0, 4000)}`;

    const response = await ai.models.generateContent({
      model: "gemini-2.5-flash",
      contents: prompt,
    });
    
    return response.text || "Unable to generate summary.";
  } catch (error) {
    throw new Error(`Failed to summarize document: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}