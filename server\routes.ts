import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import multer from "multer";
import path from "path";
import { processPDF, cleanupFile } from "./services/pdf-processor";
import { generateChatResponse } from "./services/gemini";
import { insertDocumentSchema, insertChatMessageSchema } from "@shared/schema";

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only PDF files are allowed'));
    }
  },
});

export async function registerRoutes(app: Express): Promise<Server> {
  // Upload PDF document
  app.post("/api/documents/upload", upload.single('pdf'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      const filePath = req.file.path;
      const originalName = req.file.originalname;

      // Process the PDF
      const processed = await processPDF(filePath);
      
      // Create document record
      const documentData = {
        filename: req.file.filename,
        originalName,
        size: req.file.size,
        pageCount: processed.pageCount,
        content: processed.content,
        chunks: processed.chunks,
      };

      const validation = insertDocumentSchema.safeParse(documentData);
      if (!validation.success) {
        cleanupFile(filePath);
        return res.status(400).json({ message: "Invalid document data", errors: validation.error.issues });
      }

      const document = await storage.createDocument(validation.data);
      
      // Clean up uploaded file
      cleanupFile(filePath);

      res.json(document);
    } catch (error) {
      if (req.file) {
        cleanupFile(req.file.path);
      }
      res.status(500).json({ message: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  // Get all documents
  app.get("/api/documents", async (req, res) => {
    try {
      const documents = await storage.getDocuments();
      res.json(documents);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  // Get specific document
  app.get("/api/documents/:id", async (req, res) => {
    try {
      const document = await storage.getDocument(req.params.id);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }
      res.json(document);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  // Delete document
  app.delete("/api/documents/:id", async (req, res) => {
    try {
      await storage.deleteDocument(req.params.id);
      res.json({ message: "Document deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  // Get chat messages for a document
  app.get("/api/documents/:id/messages", async (req, res) => {
    try {
      const messages = await storage.getChatMessages(req.params.id);
      res.json(messages);
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  // Send chat message
  app.post("/api/documents/:id/chat", async (req, res) => {
    try {
      const documentId = req.params.id;
      const { message } = req.body;

      if (!message || typeof message !== 'string') {
        return res.status(400).json({ message: "Message is required" });
      }

      // Get document for context
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      // Get conversation history
      const history = await storage.getChatMessages(documentId);
      const conversationHistory = history.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
      }));

      // Save user message
      const userMessageData = {
        documentId,
        role: 'user',
        content: message,
        citations: null,
      };

      const userValidation = insertChatMessageSchema.safeParse(userMessageData);
      if (!userValidation.success) {
        return res.status(400).json({ message: "Invalid message data" });
      }

      await storage.createChatMessage(userValidation.data);

      // Generate AI response
      const aiResponse = await generateChatResponse(
        message,
        document.chunks as string[],
        conversationHistory
      );

      // Save AI message
      const aiMessageData = {
        documentId,
        role: 'assistant',
        content: aiResponse.content,
        citations: aiResponse.citations,
      };

      const aiValidation = insertChatMessageSchema.safeParse(aiMessageData);
      if (!aiValidation.success) {
        return res.status(400).json({ message: "Invalid AI response data" });
      }

      const aiMessage = await storage.createChatMessage(aiValidation.data);

      res.json({
        userMessage: await storage.createChatMessage(userValidation.data),
        aiMessage,
      });
    } catch (error) {
      res.status(500).json({ message: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
